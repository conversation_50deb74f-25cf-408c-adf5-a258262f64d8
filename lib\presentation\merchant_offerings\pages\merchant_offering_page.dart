import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/app_strings.dart';
import 'package:wesell/presentation/widgets/custom_outlined_button.dart';

class MerchantOfferingPage extends StatefulWidget {
  const MerchantOfferingPage({super.key});

  @override
  State<MerchantOfferingPage> createState() => _MerchantOfferingPageState();
}

class _MerchantOfferingPageState extends State<MerchantOfferingPage> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteTheme,
      appBar: AppBar(
        backgroundColor: AppColors.whiteTheme,
        title: Text(AppStrings.offerings),
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(0.0),
        child: ListView.builder(
          itemCount: 20,
          itemBuilder: (context, index) {
            return ProductCard(
              title: "Voluptate minus iust",
              imageUrl: "https://i.pravatar.cc/150?img=5",
              productCount: "${'services.lenth'} Products",
              priceRange: "SAR ${'rangePerSales[0]'} - SAR ${'rangePerSales[1]'}",
              onEdit: (id) {
                print("Edit $id");
              },
              onDelete: (id) {
                print("Delete $id");
              },
              onAddJob: (id) {
                print("Add Job $id");
              },
            ) ;
          },
        ),
      ),
    
    );
    
  }
}


class ProductCard extends StatelessWidget {
  final String title;
  final String imageUrl;
  final String productCount;
  final String priceRange;
  final  Function(String)? onEdit;
  final  Function(String)? onDelete;
  final  Function(String)? onAddJob;

  const ProductCard({
    super.key,
    required this.title,
    required this.imageUrl,
    required this.productCount,
    required this.priceRange,
    this.onEdit,
    this.onDelete,
    this.onAddJob,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Row: Image + Title & Subtitle
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(30),
                child: Image.network(
                  imageUrl,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      productCount,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
    
          /// Price Range
          Row(
            children: [
              const Icon(Icons.image, size: 16, color: Colors.grey),
              const SizedBox(width: 6),
              Text(
                "Range Per Sale : ",
                style: TextStyle(color: Colors.grey[600]),
              ),
              Text(
                priceRange,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
          const SizedBox(height: 14),
    
          /// Action Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomOutlinedButton(
                onPressed: onAddJob != null ? () => onAddJob!("id") : null,
                label: "New Job",
                icon: Icons.add_box_outlined,
                iconColor: AppColors.primaryTheme,
                textColor: const Color(0xFF38B000),
                borderColor: const Color(0xFF38B000),
                width: 100,
                height: 40,
                borderRadius: 8,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              const SizedBox(width: 10),
              Row(
                children: [
                  IconButton(
                    onPressed: onEdit != null ? () => onEdit!("id") : null,
                    icon: const Icon(Icons.edit, color: Colors.grey),
                  ),
                  IconButton(
                    onPressed: onDelete != null ? () => onDelete!("id") : null,
                    icon: const Icon(Icons.delete_outline, color: Colors.grey),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),
          Divider(color:AppColors.borderTheme,thickness: 1,),
        ],
      ),
    );
  }
}