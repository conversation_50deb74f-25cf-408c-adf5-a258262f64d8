import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/data/models/merchant_counter_model.dart';
import 'package:wesell/data/models/activity_model.dart';
import 'package:wesell/domain/usecases/get_merchant_counter_usecase.dart';
import 'package:wesell/domain/usecases/get_activities_usecase.dart';

part 'merchant_counter_event.dart';
part 'merchant_counter_state.dart';

class MerchantStatsBloc extends Bloc<MerchantStatsEvent, MerchantStatsState> {
  final GetMerchantStats getMerchantStats;


  MerchantStatsBloc({
    required this.getMerchantStats,
  }) : super(MerchantStatsInitial()) {
    on<GetMerchantStatsEvent>(_onGetMerchantStats);
  }

  Future<void> _onGetMerchantStats(
    GetMerchantStatsEvent event,
    Emitter<MerchantStatsState> emit,
  ) async {
    emit(MerchantStatsLoading());

    final result = await getMerchantStats(event.duration);

    result.fold(
      (failure) => emit(MerchantStatsError(message: failure.message)),
      (stats) => emit(MerchantStatsLoaded(stats: stats)),
    );
  }
}

