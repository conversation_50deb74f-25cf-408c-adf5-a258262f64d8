import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wesell/presentation/marchant_dashboard/blocs/acticity/activity_bloc.dart';
import '../network/dio_client.dart';
import '../../data/services/token_refresh_service.dart';
import '../../data/datasources/merchant_counter_remote_data_source.dart';
import '../../data/repositories/merchant_counter_repository_impl.dart';
import '../../domain/repositories/merchant_counter_repository.dart';
import '../../domain/usecases/get_merchant_counter_usecase.dart';
import '../../domain/usecases/get_activities_usecase.dart';
import '../../presentation/marchant_dashboard/blocs/marchant_counter/merchant_counter_bloc.dart';
import '../../data/datasources/auth_remote_data_source.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/register_usecase.dart';
import '../../domain/usecases/forgot_password_usecase.dart';
import '../../domain/usecases/upload_file_usecase.dart';
import '../../domain/usecases/update_company_details_usecase.dart';
import '../../domain/usecases/update_industry_details_usecase.dart';
import '../../domain/usecases/update_company_locations_usecase.dart';
import '../../domain/usecases/update_digital_information_usecase.dart';
import '../../domain/usecases/update_legal_documents_usecase.dart';
import '../../domain/usecases/update_bank_details_usecase.dart';
import '../../domain/usecases/update_profile_progress_usecase.dart';
import '../../domain/repositories/media_repository.dart';
import '../../data/repositories/media_repository_impl.dart';
import '../../data/datasources/media_remote_data_source.dart';
import '../../data/datasources/profile_marchant_remote_data_source.dart';
import '../../data/datasources/territory_remote_data_source.dart';
import '../../data/datasources/industry_remote_data_source.dart';
import '../../data/repositories/profile_marchant_repository_impl.dart';
import '../../data/repositories/territory_repository_impl.dart';
import '../../data/repositories/industry_repository_impl.dart';
import '../../domain/repositories/profile_marchant_repository.dart';
import '../../domain/repositories/territory_repository.dart';
import '../../domain/repositories/industry_repository.dart';
import '../../domain/usecases/get_profile_usecase.dart';
import '../../domain/usecases/get_countries_usecase.dart';
import '../../domain/usecases/get_cities_usecase.dart';
import '../../domain/usecases/get_industries_usecase.dart';
import '../../domain/usecases/get_file_metadata_usecase.dart';
import '../../presentation/auth/bloc/login/login_bloc.dart';
import '../../presentation/auth/bloc/signup/signup_bloc.dart';
import '../../presentation/profile_marchant/bloc/profile_marchant_bloc.dart';

final serviceLocator = GetIt.instance;

Future<void> init() async {
  // External dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  serviceLocator.registerLazySingleton(() => sharedPreferences);
  
  // Core
  serviceLocator.registerLazySingleton(() => DioClient());
  serviceLocator.registerLazySingleton(() => TokenRefreshService(
    remoteDataSource: serviceLocator(),
  ));
  
  // Data sources
  serviceLocator.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(dioClient: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<MediaRemoteDataSource>(
    () => MediaRemoteDataSourceImpl(dioClient: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<ProfileMarchantRemoteDataSource>(
    () => ProfileMarchantRemoteDataSourceImpl(dioClient: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<TerritoryRemoteDataSource>(
    () => TerritoryRemoteDataSourceImpl(dioClient: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<IndustryRemoteDataSource>(
    () => IndustryRemoteDataSourceImpl(dioClient: serviceLocator()),
  );
  
  // Repositories
  serviceLocator.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: serviceLocator(),
      sharedPreferences: serviceLocator(),
    ),
  );
  serviceLocator.registerLazySingleton<MediaRepository>(
    () => MediaRepositoryImpl(remoteDataSource: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<ProfileMarchantRepository>(
    () => ProfileMarchantRepositoryImpl(remoteDataSource: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<TerritoryRepository>(
    () => TerritoryRepositoryImpl(remoteDataSource: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<IndustryRepository>(
    () => IndustryRepositoryImpl(remoteDataSource: serviceLocator()),
  );

  // Use cases
  serviceLocator.registerLazySingleton(() => LoginUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => LogoutUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => RegisterUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => ForgotPasswordUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UploadFileUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => GetProfileUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UpdateProfileProgressUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UpdateCompanyDetailsUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UpdateIndustryDetailsUseCase(serviceLocator()));

  // Merchant Stats
  serviceLocator.registerLazySingleton<MerchantStatsRemoteDataSource>(
    () => MerchantStatsRemoteDataSourceImpl(dioClient: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<MerchantStatsRepository>(
    () => MerchantStatsRepositoryImpl(remoteDataSource: serviceLocator()),
  );
  serviceLocator.registerLazySingleton(() => GetMerchantStats(serviceLocator()));
  serviceLocator.registerLazySingleton(() => GetActivities(serviceLocator()));
  serviceLocator.registerFactory(() => MerchantStatsBloc(
    getMerchantStats: serviceLocator(),
  ));
  serviceLocator.registerFactory(() => ActivityBloc(
    getActivities: serviceLocator(),
  ));
  serviceLocator.registerLazySingleton(() => UpdateCompanyLocationsUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UpdateDigitalInformationUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UpdateLegalDocumentsUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UpdateBankDetailsUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => GetCountriesUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => GetCitiesUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => GetIndustriesUseCase(serviceLocator()));
  serviceLocator.registerLazySingleton(() => GetFileMetadataUseCase(serviceLocator()));

  // Blocs
  serviceLocator.registerFactory(
    () => LoginBloc(
      loginUseCase: serviceLocator(),
      logoutUseCase: serviceLocator(),
      forgotPasswordUseCase: serviceLocator(),
    ),
  );
  serviceLocator.registerFactory(
    () => SignupBloc(
      registerUseCase: serviceLocator(),
      uploadFileUseCase: serviceLocator(),
    ),
  );
  serviceLocator.registerLazySingleton(
    () => ProfileMarchantBloc(
      getProfileUseCase: serviceLocator(),
      uploadFileUseCase: serviceLocator(),
      updateCompanyDetailsUseCase: serviceLocator(),
      updateIndustryDetailsUseCase: serviceLocator(),
      updateCompanyLocationsUseCase: serviceLocator(),
      updateDigitalInformationUseCase: serviceLocator(),
      updateLegalDocumentsUseCase: serviceLocator(),
      updateBankDetailsUseCase: serviceLocator(),
      getCountriesUseCase: serviceLocator(),
      getCitiesUseCase: serviceLocator(),
      getIndustriesUseCase: serviceLocator(),
      getFileMetadataUseCase: serviceLocator(),
      updateProfileProgressUseCase: serviceLocator(),
    ),
  );
}
