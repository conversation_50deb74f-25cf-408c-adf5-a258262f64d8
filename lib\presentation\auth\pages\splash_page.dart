import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/img_string.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/utils/preferences.dart';
import '../../../core/constants/app_helper.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    _checkAuthenticationStatus();
  }

  Future<void> _checkAuthenticationStatus() async {
    // Add a small delay for splash effect
    await Future.delayed(const Duration(seconds: 1));

    try {
      // Check if user has valid token
      final token = await Preferences.getAccessToken();
      final user = await Preferences.getUser();

      if (token != null && user != null) {
        // User is logged in, set global user and navigate to tabs
        AppHelper.user = user;
        if (mounted) {
          AppRoutes.pushRoot(context, AppRoutes.tabs);
        }
      } else {
        // User is not logged in, navigate to login
        if (mounted) {
          AppRoutes.pushRoot(context, AppRoutes.login);
        }
      }
    } catch (e) {
      // Error occurred, navigate to login
      if (mounted) {
        AppRoutes.pushRoot(context, AppRoutes.login);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    double size = MediaQuery.of(context).size.width/1.5;
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // You can add your app logo here
            Image.asset(ImageStrings.appLogo,width: size,  height: size),
            // FlutterLogo(size: 100),
            const SizedBox(height: 20),
            const CircularProgressIndicator( strokeWidth: 2,color: AppColors.primaryTheme ),
            const SizedBox(height: 20),
            const Text(
              'Loading...',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}
